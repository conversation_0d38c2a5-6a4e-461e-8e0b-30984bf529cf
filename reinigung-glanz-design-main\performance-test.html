<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SUZ Performance & Animation Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
        }
        .test-section {
            background: rgba(28, 28, 30, 0.8);
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .test-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        .test-button:hover {
            background: #2563eb;
            transform: translateY(-2px);
        }
        .status-excellent { color: #10b981; }
        .status-good { color: #f59e0b; }
        .status-poor { color: #ef4444; }
        .test-result {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 14px;
        }
        .metric-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .metric-card {
            background: rgba(0, 0, 0, 0.4);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            margin: 10px 0;
        }
        iframe {
            width: 100%;
            height: 400px;
            border: 1px solid #333;
            border-radius: 10px;
            margin: 20px 0;
        }
        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3b82f6, #10b981);
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🚀 SUZ Performance & Animation Test Suite</h1>
        
        <div class="test-section">
            <h2>⚡ Real-time Performance Metrics</h2>
            <div class="metric-grid">
                <div class="metric-card">
                    <div>FPS</div>
                    <div class="metric-value" id="fpsValue">--</div>
                    <div id="fpsStatus">Measuring...</div>
                </div>
                <div class="metric-card">
                    <div>Frame Time</div>
                    <div class="metric-value" id="frameTimeValue">--</div>
                    <div>ms</div>
                </div>
                <div class="metric-card">
                    <div>GPU Usage</div>
                    <div class="metric-value" id="gpuValue">--</div>
                    <div id="gpuStatus">Detecting...</div>
                </div>
                <div class="metric-card">
                    <div>Memory</div>
                    <div class="metric-value" id="memoryValue">--</div>
                    <div>MB</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>🎬 Animation Performance Tests</h2>
            <button class="test-button" onclick="testScrollAnimation()">Test Scroll Animation</button>
            <button class="test-button" onclick="testGlassEffects()">Test Glass Morphism</button>
            <button class="test-button" onclick="testHoverEffects()">Test Hover Effects</button>
            <button class="test-button" onclick="testReducedMotion()">Test Reduced Motion</button>
            <div id="animationResults"></div>
        </div>

        <div class="test-section">
            <h2>🔧 Hardware Acceleration Test</h2>
            <button class="test-button" onclick="testHardwareAcceleration()">Test GPU Acceleration</button>
            <div id="hardwareResults"></div>
        </div>

        <div class="test-section">
            <h2>♿ Accessibility Compliance</h2>
            <button class="test-button" onclick="testAccessibility()">Test Accessibility Features</button>
            <div id="accessibilityResults"></div>
        </div>

        <div class="test-section">
            <h2>📊 Performance Score</h2>
            <div class="progress-bar">
                <div class="progress-fill" id="overallProgress"></div>
            </div>
            <div id="overallScore">Run tests to see overall performance score</div>
        </div>

        <div class="test-section">
            <h2>🌐 Website Preview</h2>
            <iframe id="websiteFrame" src="http://localhost:8082"></iframe>
        </div>
    </div>

    <script>
        let performanceData = {
            fps: 0,
            frameTime: 0,
            gpuAccelerated: false,
            memoryUsage: 0,
            animationScore: 0,
            accessibilityScore: 0
        };

        // Real-time FPS monitoring
        function startFPSMonitoring() {
            let frameCount = 0;
            let lastTime = performance.now();
            let fps = 0;

            function measureFPS() {
                frameCount++;
                const currentTime = performance.now();
                const deltaTime = currentTime - lastTime;

                if (deltaTime >= 1000) {
                    fps = Math.round((frameCount * 1000) / deltaTime);
                    frameCount = 0;
                    lastTime = currentTime;

                    // Update UI
                    document.getElementById('fpsValue').textContent = fps;
                    document.getElementById('frameTimeValue').textContent = (1000 / fps).toFixed(1);
                    
                    const fpsStatus = document.getElementById('fpsStatus');
                    if (fps >= 55) {
                        fpsStatus.textContent = 'Excellent';
                        fpsStatus.className = 'status-excellent';
                    } else if (fps >= 30) {
                        fpsStatus.textContent = 'Good';
                        fpsStatus.className = 'status-good';
                    } else {
                        fpsStatus.textContent = 'Poor';
                        fpsStatus.className = 'status-poor';
                    }

                    performanceData.fps = fps;
                    performanceData.frameTime = 1000 / fps;
                }

                requestAnimationFrame(measureFPS);
            }

            requestAnimationFrame(measureFPS);
        }

        // Memory monitoring
        function monitorMemory() {
            if (performance.memory) {
                const memoryMB = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);
                document.getElementById('memoryValue').textContent = memoryMB;
                performanceData.memoryUsage = memoryMB;
            } else {
                document.getElementById('memoryValue').textContent = 'N/A';
            }
        }

        // Test scroll animation performance
        function testScrollAnimation() {
            const results = document.getElementById('animationResults');
            results.innerHTML = '<div class="test-result">Testing scroll animation performance...</div>';

            // Simulate scroll animation test
            let frameCount = 0;
            const startTime = performance.now();
            const duration = 3000;

            function testFrame() {
                frameCount++;
                const currentTime = performance.now();

                if (currentTime - startTime < duration) {
                    requestAnimationFrame(testFrame);
                } else {
                    const avgFPS = Math.round((frameCount * 1000) / duration);
                    let status, className;

                    if (avgFPS >= 55) {
                        status = 'Excellent (60fps)';
                        className = 'status-excellent';
                        performanceData.animationScore += 25;
                    } else if (avgFPS >= 30) {
                        status = 'Good (30fps+)';
                        className = 'status-good';
                        performanceData.animationScore += 15;
                    } else {
                        status = 'Poor (<30fps)';
                        className = 'status-poor';
                        performanceData.animationScore += 5;
                    }

                    results.innerHTML = `
                        <div class="test-result">
                            <strong>Scroll Animation Test:</strong><br>
                            Average FPS: <span class="${className}">${avgFPS}</span><br>
                            Status: <span class="${className}">${status}</span>
                        </div>
                    `;
                    updateOverallScore();
                }
            }

            requestAnimationFrame(testFrame);
        }

        // Test glass morphism effects
        function testGlassEffects() {
            const results = document.getElementById('animationResults');
            const testElement = document.createElement('div');
            testElement.style.backdropFilter = 'blur(20px)';
            testElement.style.webkitBackdropFilter = 'blur(20px)';

            const supportsBackdropFilter = testElement.style.backdropFilter !== '' || 
                                         testElement.style.webkitBackdropFilter !== '';

            let score = supportsBackdropFilter ? 25 : 10;
            performanceData.animationScore += score;

            results.innerHTML += `
                <div class="test-result">
                    <strong>Glass Morphism Test:</strong><br>
                    Backdrop Filter: <span class="${supportsBackdropFilter ? 'status-excellent' : 'status-poor'}">${supportsBackdropFilter ? 'Supported' : 'Not Supported'}</span><br>
                    Performance Impact: <span class="${supportsBackdropFilter ? 'status-good' : 'status-excellent'}">
                        ${supportsBackdropFilter ? 'Moderate (GPU accelerated)' : 'Low (CSS fallback)'}
                    </span>
                </div>
            `;
            updateOverallScore();
        }

        // Test hardware acceleration
        function testHardwareAcceleration() {
            const results = document.getElementById('hardwareResults');
            const testElement = document.createElement('div');
            testElement.style.transform = 'translateZ(0)';
            testElement.style.willChange = 'transform';
            document.body.appendChild(testElement);

            const computedStyle = window.getComputedStyle(testElement);
            const hasTransform3D = computedStyle.transform.includes('matrix3d') || 
                                  computedStyle.transform.includes('translate3d');
            
            document.body.removeChild(testElement);

            performanceData.gpuAccelerated = hasTransform3D;
            document.getElementById('gpuValue').textContent = hasTransform3D ? 'Active' : 'Inactive';
            document.getElementById('gpuStatus').textContent = hasTransform3D ? 'GPU Accelerated' : 'CPU Rendering';
            document.getElementById('gpuStatus').className = hasTransform3D ? 'status-excellent' : 'status-poor';

            results.innerHTML = `
                <div class="test-result">
                    <strong>Hardware Acceleration Test:</strong><br>
                    3D Transforms: <span class="${hasTransform3D ? 'status-excellent' : 'status-poor'}">${hasTransform3D ? 'Supported' : 'Not Supported'}</span><br>
                    GPU Acceleration: <span class="${hasTransform3D ? 'status-excellent' : 'status-poor'}">${hasTransform3D ? 'Active' : 'Inactive'}</span><br>
                    Performance: <span class="${hasTransform3D ? 'status-excellent' : 'status-good'}">${hasTransform3D ? 'Optimized' : 'Standard'}</span>
                </div>
            `;
        }

        // Test accessibility features
        function testAccessibility() {
            const results = document.getElementById('accessibilityResults');
            const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
            const hasAriaLabels = document.querySelectorAll('[aria-label]').length > 0;
            const hasSkipLinks = document.querySelectorAll('a[href^="#"]').length > 0;

            let score = 0;
            if (prefersReducedMotion) score += 30;
            if (hasAriaLabels) score += 20;
            if (hasSkipLinks) score += 10;

            performanceData.accessibilityScore = score;

            results.innerHTML = `
                <div class="test-result">
                    <strong>Accessibility Test:</strong><br>
                    Reduced Motion: <span class="${prefersReducedMotion ? 'status-excellent' : 'status-good'}">${prefersReducedMotion ? 'Respected' : 'Not Set'}</span><br>
                    ARIA Labels: <span class="${hasAriaLabels ? 'status-excellent' : 'status-poor'}">${hasAriaLabels ? 'Present' : 'Missing'}</span><br>
                    Skip Links: <span class="${hasSkipLinks ? 'status-excellent' : 'status-good'}">${hasSkipLinks ? 'Available' : 'Not Found'}</span><br>
                    Score: <span class="status-${score >= 50 ? 'excellent' : score >= 30 ? 'good' : 'poor'}">${score}/60</span>
                </div>
            `;
            updateOverallScore();
        }

        // Update overall performance score
        function updateOverallScore() {
            const fpsScore = performanceData.fps >= 55 ? 30 : performanceData.fps >= 30 ? 20 : 10;
            const memoryScore = performanceData.memoryUsage < 50 ? 20 : performanceData.memoryUsage < 100 ? 15 : 10;
            const gpuScore = performanceData.gpuAccelerated ? 20 : 10;
            
            const totalScore = fpsScore + memoryScore + gpuScore + 
                             Math.min(performanceData.animationScore, 30) + 
                             Math.min(performanceData.accessibilityScore, 20);
            
            const maxScore = 120;
            const percentage = Math.round((totalScore / maxScore) * 100);
            
            document.getElementById('overallProgress').style.width = percentage + '%';
            
            let rating;
            if (percentage >= 90) rating = 'Excellent';
            else if (percentage >= 75) rating = 'Good';
            else if (percentage >= 60) rating = 'Fair';
            else rating = 'Needs Improvement';
            
            document.getElementById('overallScore').innerHTML = `
                Overall Performance Score: <strong>${percentage}%</strong> (${rating})<br>
                <small>FPS: ${fpsScore}/30 | Memory: ${memoryScore}/20 | GPU: ${gpuScore}/20 | Animation: ${Math.min(performanceData.animationScore, 30)}/30 | A11y: ${Math.min(performanceData.accessibilityScore, 20)}/20</small>
            `;
        }

        // Initialize monitoring
        startFPSMonitoring();
        setInterval(monitorMemory, 2000);
        
        // Auto-run basic tests after 3 seconds
        setTimeout(() => {
            testHardwareAcceleration();
            testAccessibility();
        }, 3000);
    </script>
</body>
</html>
