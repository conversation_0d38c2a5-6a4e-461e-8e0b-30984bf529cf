import{j as t}from"./ui-vendor-WY5xTeZk.js";import{r as c}from"./react-vendor-o6ozJo2K.js";import{c as i}from"./index-C_fHf9Ba.js";import{H as y}from"./house-CewbeNug.js";import"./utils-vendor--BulIq_u.js";import"./query-vendor--kaXPEoe.js";import"./router-vendor-CClBJTgV.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const f=i("Bed",[["path",{d:"M2 4v16",key:"vw9hq8"}],["path",{d:"M2 8h18a2 2 0 0 1 2 2v10",key:"1dgv2r"}],["path",{d:"M2 17h20",key:"18nfp3"}],["path",{d:"M6 8v9",key:"1yriud"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const v=i("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const x=i("GraduationCap",[["path",{d:"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z",key:"j76jl0"}],["path",{d:"M22 10v6",key:"1lu8f3"}],["path",{d:"M6 12.5V16a6 3 0 0 0 12 0v-3.5",key:"1r8lef"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const g=i("ShoppingBag",[["path",{d:"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z",key:"hou9p0"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const b=i("Stethoscope",[["path",{d:"M11 2v2",key:"1539x4"}],["path",{d:"M5 2v2",key:"1yf1q8"}],["path",{d:"M5 3H4a2 2 0 0 0-2 2v4a6 6 0 0 0 12 0V5a2 2 0 0 0-2-2h-1",key:"rb5t3r"}],["path",{d:"M8 15a6 6 0 0 0 12 0v-3",key:"x18d4x"}],["circle",{cx:"20",cy:"10",r:"2",key:"ts1r5v"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const k=i("Utensils",[["path",{d:"M3 2v7c0 1.1.9 2 2 2h4a2 2 0 0 0 2-2V2",key:"cjf0a3"}],["path",{d:"M7 2v20",key:"1473qp"}],["path",{d:"M21 15V2a5 5 0 0 0-5 5v6c0 1.1.9 2 2 2h3Zm0 0v7",key:"j28e5"}]]),m=[{id:"1",name:"Hotel Excelsior Ernst Köln",type:"hotel"},{id:"2",name:"Büropark Rheinauhafen",type:"office"},{id:"3",name:"Universitätsklinikum Bonn",type:"medical"},{id:"4",name:"Wohnpark Deutzer Freiheit",type:"residential"},{id:"5",name:"Restaurant Hanse-Klause",type:"restaurant"},{id:"6",name:"Galeria Kaufhof Köln",type:"retail"},{id:"7",name:"Hotel Königshof Bonn",type:"hotel"},{id:"8",name:"Bürohaus MediaPark",type:"office"},{id:"9",name:"Praxis Dr. Müller-Weber",type:"medical"},{id:"10",name:"Residenz Rheinblick",type:"residential"},{id:"11",name:"Brauhaus Sion",type:"restaurant"},{id:"12",name:"Rhein-Center Köln",type:"retail"},{id:"13",name:"Steigenberger Grandhotel",type:"hotel"},{id:"14",name:"Krankenhaus Porz",type:"medical"},{id:"15",name:"Universität zu Köln",type:"school"},{id:"16",name:"Wohnquartier Ehrenfeld",type:"residential"},{id:"17",name:"Restaurant Himmel un Ääd",type:"restaurant"},{id:"18",name:"Büroturm KölnTriangle",type:"office"},{id:"19",name:"Shopping Arkaden Bonn",type:"retail"},{id:"20",name:"Rheinische Fachhochschule",type:"school"}],w=n=>({hotel:f,office:v,medical:b,residential:y,retail:g,restaurant:k,school:x})[n],R=()=>{const n=c.useRef(null),s=[...m,...m];return c.useEffect(()=>{const e=n.current;if(!e)return;const r=window.matchMedia("(prefers-reduced-motion: reduce)").matches,a=window.matchMedia("(max-width: 768px)").matches,p=window.matchMedia("(max-width: 480px)").matches;if(r){e.style.animationPlayState="paused";return}a&&(e.style.animationPlayState="running",e.style.transform="translateZ(0)",e.style.willChange="transform",e.style.webkitAnimationPlayState="running",e.style.webkitTransform="translateZ(0)");const l=new IntersectionObserver(h=>{h.forEach(u=>{u.isIntersecting?r||(e.style.animationPlayState="running",e.style.webkitAnimationPlayState="running",a&&(e.style.transform="translateZ(0)",e.style.webkitTransform="translateZ(0)")):a||(e.style.animationPlayState="paused")})},{threshold:p?.05:.1,rootMargin:a?"50px":"0px"});l.observe(e);const d=()=>{window.matchMedia("(max-width: 768px)").matches&&!r&&(e.style.animationPlayState="running",e.style.webkitAnimationPlayState="running",e.style.transform="translateZ(0)",e.style.webkitTransform="translateZ(0)")};return window.addEventListener("resize",d),()=>{l.disconnect(),window.removeEventListener("resize",d)}},[]),t.jsxs("section",{className:"suz-company-showcase overflow-hidden relative","aria-label":"Unsere Kunden und Partner",role:"region",children:[t.jsxs("div",{className:"text-center mb-16 px-4 animate-fade-in",children:[t.jsxs("h2",{className:"suz-section-title text-slate-100 mb-8",children:["Vertrauen von ",t.jsx("span",{className:"gradient-text",children:"führenden Unternehmen"})]}),t.jsx("p",{className:"suz-text-heading-lg text-slate-300 max-w-3xl mx-auto font-light",children:"Über 500 zufriedene Kunden vertrauen auf unsere professionellen Reinigungsdienstleistungen"})]}),t.jsxs("div",{className:"relative",children:[t.jsx("div",{className:"absolute left-0 top-0 bottom-0 w-32 bg-gradient-to-r from-black via-black/90 to-transparent z-10 pointer-events-none"}),t.jsx("div",{className:"absolute right-0 top-0 bottom-0 w-32 bg-gradient-to-l from-black via-black/90 to-transparent z-10 pointer-events-none"}),t.jsx("div",{ref:n,className:"suz-company-scroll flex gap-10 animate-scroll-right",role:"list","aria-label":"Kontinuierlich scrollende Liste unserer Kunden","aria-live":"polite","aria-atomic":"false",children:s.map((e,r)=>{const a=w(e.type);return t.jsx("div",{className:"suz-company-card flex-shrink-0",role:"listitem","aria-label":`Kunde: ${e.name}, Kategorie: ${o(e.type)}`,children:t.jsxs("div",{className:"suz-card-glass glass-morphism-premium rounded-3xl border border-white/30 shadow-2xl hover:shadow-3xl transition-all duration-500 group relative overflow-hidden",children:[t.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 group-hover:animate-shimmer transition-opacity duration-500 pointer-events-none"}),t.jsxs("div",{className:"suz-company-card-content text-center relative z-10",children:[t.jsx("div",{className:"mb-4 flex justify-center",children:t.jsx("div",{className:"icon-badge-enhanced group-hover:scale-110 group-hover:rotate-3 transition-all duration-500",children:t.jsx(a,{size:32,className:"text-blue-400 drop-shadow-lg transition-all duration-500 group-hover:text-blue-300 group-hover:scale-110",strokeWidth:2})})}),t.jsx("h3",{className:"suz-text-body-lg font-semibold text-slate-100 mb-3 group-hover:text-white transition-colors duration-300",children:e.name}),t.jsx("div",{className:"mt-3",children:t.jsx("span",{className:"inline-block px-4 py-2 text-sm font-medium rounded-full bg-gradient-to-r from-blue-900/40 to-blue-800/40 text-blue-300 border border-blue-600/50 backdrop-blur-sm group-hover:from-blue-800/50 group-hover:to-blue-700/50 group-hover:text-blue-200 transition-all duration-300","aria-label":`Unternehmenstyp: ${o(e.type)}`,children:o(e.type)})})]})]})},`${e.id}-${r}`)})})]})]})},o=n=>({hotel:"Hotel",office:"Büro",medical:"Medizin",residential:"Wohnanlage",retail:"Einzelhandel",restaurant:"Gastronomie",school:"Bildung"})[n];export{R as default};
