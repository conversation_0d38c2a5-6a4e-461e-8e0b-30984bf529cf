import{j as e}from"./ui-vendor-WY5xTeZk.js";import"./react-vendor-o6ozJo2K.js";const o=()=>{const t=[{name:"<PERSON>",role:"<PERSON><PERSON><PERSON><PERSON><PERSON>führer",image:"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=300&h=300&fit=crop&crop=face",isLeadership:!0},{name:"<PERSON><PERSON>",role:"Marketing und Communications Manager",image:"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=300&fit=crop&crop=face",isLeadership:!0},{name:"<PERSON>",role:"Teamleitung",image:"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=300&h=300&fit=crop&crop=face"},{name:"<PERSON>",role:"Fachkraft",image:"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=300&h=300&fit=crop&crop=face"},{name:"<PERSON><PERSON>",role:"Fachkraft",image:"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=300&fit=crop&crop=face"},{name:"Anna Weber",role:"Qualitätskontrolle",image:"https://images.unsplash.com/photo-1531123897727-8f129e1688ce?w=300&h=300&fit=crop&crop=face"}];return e.jsx("section",{id:"team",className:"px-4",style:{padding:"var(--section-padding-xl) var(--space-4)",marginTop:"var(--space-16)"},children:e.jsxs("div",{className:"max-w-7xl mx-auto",children:[e.jsxs("div",{className:"text-center animate-fade-in",style:{marginBottom:"var(--space-20)"},children:[e.jsxs("h2",{className:"suz-section-title text-slate-100 mb-8",children:["Unser ",e.jsx("span",{className:"gradient-text",children:"Team"})]}),e.jsx("p",{className:"suz-text-heading-xl text-slate-300 max-w-3xl mx-auto",children:"Professionelle Reinigungskräfte mit Leidenschaft für Sauberkeit"})]}),e.jsx("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-8",children:t.map((a,s)=>e.jsxs("article",{className:`suz-card-glass team-card rounded-3xl border text-center group shadow-2xl animate-fade-in ${a.isLeadership?"border-blue-400/50 bg-gradient-to-br from-blue-900/40 to-slate-800/60 leadership-card":"border-white/30"}`,style:{animationDelay:`${s*.15}s`,padding:"var(--component-padding-lg)"},children:[e.jsx("div",{className:"mb-6",children:e.jsx("img",{src:a.image,alt:`${a.name} - ${a.role} bei SUZ Reinigung`,className:`w-28 h-28 rounded-full mx-auto object-cover shadow-lg image-optimized group-hover:scale-105 transition-transform duration-300 ${a.isLeadership?"border-4 border-blue-400/60":"border-4 border-white/50"}`,loading:"lazy",decoding:"async"})}),e.jsx("h3",{className:"suz-text-heading-lg font-semibold text-slate-100 mb-3",children:a.name}),e.jsx("p",{className:`suz-text-body-lg font-medium ${a.isLeadership?"text-blue-300 font-semibold":"text-blue-400"}`,children:a.role}),a.isLeadership&&e.jsx("div",{className:"mt-3",children:e.jsx("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-900/50 text-blue-200 border border-blue-700/50",children:"Führungsteam"})})]},s))})]})})};export{o as default};
