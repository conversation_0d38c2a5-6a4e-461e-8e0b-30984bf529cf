import{j as e}from"./ui-vendor-WY5xTeZk.js";import{c as a}from"./index-C_fHf9Ba.js";import{U as t}from"./users-CuNJVDcq.js";import"./react-vendor-o6ozJo2K.js";import"./utils-vendor--BulIq_u.js";import"./query-vendor--kaXPEoe.js";import"./router-vendor-CClBJTgV.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const r=a("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const s=a("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]),x=()=>e.jsx("section",{id:"contact",className:"px-4",style:{padding:"var(--section-padding-xl) var(--space-4)",marginTop:"var(--space-16)"},children:e.jsx("div",{className:"max-w-5xl mx-auto text-center",children:e.jsxs("div",{className:"suz-card-glass rounded-3xl border border-white/30 animate-fade-in shadow-2xl",style:{padding:"var(--space-16)"},children:[e.jsxs("h2",{className:"suz-section-title text-slate-100 mb-8",children:[e.jsx("span",{className:"gradient-text",children:"Kontakt"})," aufnehmen"]}),e.jsx("p",{className:"suz-text-heading-lg text-slate-300 mb-12",children:"Kontaktieren Sie uns jetzt – schnell & unkompliziert!"}),e.jsxs("div",{className:"flex flex-col sm:flex-row justify-center gap-8 mb-16",children:[e.jsxs("a",{href:"https://wa.me/*************",target:"_blank",rel:"noopener noreferrer",className:"suz-btn-primary bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 px-12 py-6 rounded-full text-xl shadow-2xl flex items-center justify-center gap-3","aria-label":"Kontakt über WhatsApp aufnehmen",children:[e.jsx(s,{className:"w-6 h-6","aria-hidden":"true"}),"WhatsApp"]}),e.jsxs("a",{href:"mailto:<EMAIL>",className:"suz-btn-primary bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 px-12 py-6 rounded-full text-xl shadow-2xl flex items-center justify-center gap-3","aria-label":"E-Mail an SUZ Reinigung senden",children:[e.jsx(r,{className:"w-6 h-6","aria-hidden":"true"}),"E-Mail"]})]}),e.jsxs("div",{className:"grid md:grid-cols-2 gap-12 text-slate-300",children:[e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsx(t,{className:"w-10 h-10 mb-4",style:{color:"var(--suz-blue-primary)"},"aria-hidden":"true"}),e.jsx("h3",{className:"suz-text-heading-md font-semibold text-slate-100 mb-4",children:"Unser Standort"}),e.jsxs("address",{className:"suz-text-body-lg not-italic text-center",children:["Paul-Langen-Straße 39",e.jsx("br",{}),"53229 Bonn",e.jsx("br",{}),"Deutschland"]})]}),e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsx(s,{className:"w-10 h-10 mb-4",style:{color:"var(--suz-blue-primary)"},"aria-hidden":"true"}),e.jsx("h3",{className:"suz-text-heading-md font-semibold text-slate-100 mb-4",children:"Direkte Kontaktaufnahme"}),e.jsxs("div",{className:"text-center space-y-2",children:[e.jsx("p",{className:"suz-text-body-lg",children:e.jsx("a",{href:"tel:+*************",className:"hover:text-blue-600 transition-colors font-medium",children:"+49 176 23152477"})}),e.jsx("p",{className:"suz-text-body-lg",children:e.jsx("a",{href:"mailto:<EMAIL>",className:"hover:text-blue-600 transition-colors font-medium",children:"<EMAIL>"})})]})]})]})]})})});export{x as default};
