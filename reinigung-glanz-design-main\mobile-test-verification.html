<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SUZ Mobile Responsiveness Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            background: rgba(28, 28, 30, 0.8);
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .test-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background: #2563eb;
        }
        .status-good { color: #10b981; }
        .status-warning { color: #f59e0b; }
        .status-error { color: #ef4444; }
        .test-result {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-family: monospace;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: 1px solid #333;
            border-radius: 10px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔍 SUZ Mobile Responsiveness Test</h1>
        
        <div class="test-section">
            <h2>📱 Current Viewport Information</h2>
            <div id="viewportInfo">
                <p>Width: <span id="width"></span>px</p>
                <p>Height: <span id="height"></span>px</p>
                <p>Device Pixel Ratio: <span id="dpr"></span></p>
                <p>Orientation: <span id="orientation"></span></p>
                <p>Mobile Status: <span id="mobileStatus"></span></p>
            </div>
        </div>

        <div class="test-section">
            <h2>🎯 Touch Target Test</h2>
            <p>Testing minimum 48px touch targets:</p>
            <div id="touchTargetResults"></div>
            <button class="test-button" onclick="testTouchTargets()">Run Touch Target Test</button>
        </div>

        <div class="test-section">
            <h2>🎨 Glass Morphism Test</h2>
            <p>Testing backdrop-filter support:</p>
            <div id="glassResults"></div>
            <button class="test-button" onclick="testGlassEffects()">Test Glass Effects</button>
        </div>

        <div class="test-section">
            <h2>⚡ Performance Test</h2>
            <p>Testing animation performance:</p>
            <div id="performanceResults"></div>
            <button class="test-button" onclick="testPerformance()">Test Performance</button>
        </div>

        <div class="test-section">
            <h2>🌐 Website Preview</h2>
            <iframe id="websiteFrame" src="http://localhost:8082"></iframe>
        </div>

        <div class="test-section">
            <h2>📋 Test Instructions</h2>
            <ol>
                <li>Open browser developer tools (F12)</li>
                <li>Toggle device toolbar (Ctrl+Shift+M)</li>
                <li>Test these viewport sizes:
                    <ul>
                        <li>320x568 (iPhone SE)</li>
                        <li>375x667 (iPhone 8)</li>
                        <li>414x896 (iPhone 11)</li>
                        <li>768x1024 (iPad)</li>
                    </ul>
                </li>
                <li>Verify touch interactions work smoothly</li>
                <li>Check glass morphism effects render correctly</li>
                <li>Ensure animations maintain 60fps</li>
            </ol>
        </div>
    </div>

    <script>
        function updateViewportInfo() {
            document.getElementById('width').textContent = window.innerWidth;
            document.getElementById('height').textContent = window.innerHeight;
            document.getElementById('dpr').textContent = window.devicePixelRatio || 1;
            document.getElementById('orientation').textContent = 
                window.innerWidth > window.innerHeight ? 'Landscape' : 'Portrait';
            
            const isMobile = window.innerWidth < 768;
            const statusElement = document.getElementById('mobileStatus');
            statusElement.textContent = isMobile ? 'Mobile' : 'Desktop';
            statusElement.className = isMobile ? 'status-good' : 'status-warning';
        }

        function testTouchTargets() {
            const results = document.getElementById('touchTargetResults');
            const iframe = document.getElementById('websiteFrame');
            
            try {
                // Test if we can access iframe content (same origin)
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                const buttons = iframeDoc.querySelectorAll('button, a, [role="button"]');
                
                let passCount = 0;
                let totalCount = buttons.length;
                
                buttons.forEach(button => {
                    const rect = button.getBoundingClientRect();
                    const minSize = Math.min(rect.width, rect.height);
                    if (minSize >= 48) passCount++;
                });
                
                const percentage = totalCount > 0 ? Math.round((passCount / totalCount) * 100) : 0;
                results.innerHTML = `
                    <div class="test-result">
                        <strong>Touch Target Results:</strong><br>
                        Passed: ${passCount}/${totalCount} (${percentage}%)<br>
                        Status: <span class="${percentage >= 90 ? 'status-good' : percentage >= 70 ? 'status-warning' : 'status-error'}">${percentage >= 90 ? 'Excellent' : percentage >= 70 ? 'Good' : 'Needs Improvement'}</span>
                    </div>
                `;
            } catch (e) {
                results.innerHTML = `
                    <div class="test-result">
                        <strong>Touch Target Test:</strong><br>
                        Unable to access iframe content (CORS). Please test manually by inspecting touch targets in the preview above.
                    </div>
                `;
            }
        }

        function testGlassEffects() {
            const results = document.getElementById('glassResults');
            const testElement = document.createElement('div');
            testElement.style.backdropFilter = 'blur(10px)';
            testElement.style.webkitBackdropFilter = 'blur(10px)';
            
            const supportsBackdropFilter = testElement.style.backdropFilter !== '' || 
                                         testElement.style.webkitBackdropFilter !== '';
            
            results.innerHTML = `
                <div class="test-result">
                    <strong>Glass Morphism Support:</strong><br>
                    Backdrop Filter: <span class="${supportsBackdropFilter ? 'status-good' : 'status-error'}">${supportsBackdropFilter ? 'Supported' : 'Not Supported'}</span><br>
                    Browser: ${navigator.userAgent.includes('Chrome') ? 'Chrome' : navigator.userAgent.includes('Firefox') ? 'Firefox' : navigator.userAgent.includes('Safari') ? 'Safari' : 'Other'}
                </div>
            `;
        }

        function testPerformance() {
            const results = document.getElementById('performanceResults');
            let frameCount = 0;
            let startTime = performance.now();
            
            function countFrames() {
                frameCount++;
                if (performance.now() - startTime < 1000) {
                    requestAnimationFrame(countFrames);
                } else {
                    const fps = frameCount;
                    results.innerHTML = `
                        <div class="test-result">
                            <strong>Animation Performance:</strong><br>
                            FPS: <span class="${fps >= 55 ? 'status-good' : fps >= 30 ? 'status-warning' : 'status-error'}">${fps}</span><br>
                            Status: <span class="${fps >= 55 ? 'status-good' : fps >= 30 ? 'status-warning' : 'status-error'}">${fps >= 55 ? 'Excellent (60fps)' : fps >= 30 ? 'Good (30fps+)' : 'Poor (<30fps)'}</span>
                        </div>
                    `;
                }
            }
            
            requestAnimationFrame(countFrames);
        }

        // Update viewport info on load and resize
        updateViewportInfo();
        window.addEventListener('resize', updateViewportInfo);
        
        // Auto-update every 2 seconds
        setInterval(updateViewportInfo, 2000);
    </script>
</body>
</html>
