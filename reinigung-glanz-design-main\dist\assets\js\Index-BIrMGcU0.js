const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/js/Hero-C1Va492p.js","assets/js/ui-vendor-WY5xTeZk.js","assets/js/react-vendor-o6ozJo2K.js","assets/js/CompanyShowcase-zNc2jTdf.js","assets/js/index-C_fHf9Ba.js","assets/js/utils-vendor--BulIq_u.js","assets/js/query-vendor--kaXPEoe.js","assets/js/router-vendor-CClBJTgV.js","assets/css/index-CHTkMD3A.css","assets/js/house-CewbeNug.js","assets/js/Services-DNGRjTaM.js","assets/js/users-CuNJVDcq.js","assets/js/Team-AobfuSFs.js","assets/js/Contact-C8oc20f7.js","assets/js/Footer-CL2aL3cg.js"])))=>i.map(i=>d[i]);
import{_ as c}from"./index-C_fHf9Ba.js";import{j as e}from"./ui-vendor-WY5xTeZk.js";import{r as n}from"./react-vendor-o6ozJo2K.js";import"./utils-vendor--BulIq_u.js";import"./query-vendor--kaXPEoe.js";import"./router-vendor-CClBJTgV.js";const d=768;function b(){const[l,s]=n.useState(void 0);return n.useEffect(()=>{const t=window.matchMedia(`(max-width: ${d-1}px)`),a=()=>{s(window.innerWidth<d)};return t.addEventListener("change",a),s(window.innerWidth<d),()=>t.removeEventListener("change",a)},[]),!!l}const g=({scrollToSection:l})=>{const[s,t]=n.useState(!1),a=b();n.useEffect(()=>{const i=u=>{const m=document.querySelector('[data-nav="main"]');m&&!m.contains(u.target)&&t(!1)};return s?(document.addEventListener("mousedown",i),document.body.style.overflow="hidden"):document.body.style.overflow="unset",()=>{document.removeEventListener("mousedown",i),document.body.style.overflow="unset"}},[s]),n.useEffect(()=>{const i=u=>{u.key==="Escape"&&t(!1)};return s&&document.addEventListener("keydown",i),()=>{document.removeEventListener("keydown",i)}},[s]);const o=i=>{l(i),t(!1)},p=()=>{t(!s)};return e.jsxs("nav",{className:"fixed top-6 z-50 animate-fade-in suz-navigation-enhanced",role:"navigation","aria-label":"Hauptnavigation","data-nav":"main",style:{left:"50%",transform:"translateX(-50%)",width:"auto",maxWidth:"calc(100vw - 2rem)",minWidth:"fit-content",display:"block"},children:[e.jsx("div",{className:`suz-card-glass px-4 sm:px-6 md:px-8 py-3 sm:py-4 rounded-full border border-white/30 shadow-xl ${a?"hidden":"block"}`,children:e.jsxs("div",{className:"flex items-center justify-center space-x-1 sm:space-x-2 md:space-x-4 lg:space-x-6 xl:space-x-8",children:[e.jsx("button",{type:"button",onClick:()=>o("home"),className:"suz-nav-link suz-focus-ring whitespace-nowrap","aria-label":"Zur Startseite navigieren",children:"Startseite"}),e.jsx("button",{type:"button",onClick:()=>o("services"),className:"suz-nav-link suz-focus-ring whitespace-nowrap","aria-label":"Zu unseren Leistungen navigieren",children:"Leistungen"}),e.jsx("button",{type:"button",onClick:()=>o("team"),className:"suz-nav-link suz-focus-ring whitespace-nowrap","aria-label":"Zu unserem Team navigieren",children:"Unser Team"}),e.jsx("button",{type:"button",onClick:()=>o("contact"),className:"suz-nav-link suz-focus-ring whitespace-nowrap","aria-label":"Zum Kontakt navigieren",children:"Kontakt"})]})}),a&&e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"suz-card-glass px-4 py-3 rounded-full border border-white/30 shadow-xl",children:e.jsx("button",{type:"button",onClick:p,className:"suz-mobile-menu-button suz-focus-ring","aria-label":s?"Menü schließen":"Menü öffnen","aria-expanded":s,"aria-controls":"mobile-menu",children:e.jsxs("div",{className:"suz-hamburger-icon",children:[e.jsx("span",{className:`suz-hamburger-line ${s?"suz-hamburger-line-1-open":""}`}),e.jsx("span",{className:`suz-hamburger-line ${s?"suz-hamburger-line-2-open":""}`}),e.jsx("span",{className:`suz-hamburger-line ${s?"suz-hamburger-line-3-open":""}`})]})})}),s&&e.jsx("div",{className:"suz-mobile-menu-overlay","aria-hidden":"true",children:e.jsx("div",{className:"suz-mobile-menu-backdrop",onClick:()=>t(!1)})}),e.jsx("div",{id:"mobile-menu",className:`suz-mobile-menu ${s?"suz-mobile-menu-open":"suz-mobile-menu-closed"}`,"aria-hidden":!s,children:e.jsxs("div",{className:"suz-mobile-menu-content",children:[e.jsx("button",{type:"button",onClick:()=>o("home"),className:"suz-mobile-nav-link suz-focus-ring","aria-label":"Zur Startseite navigieren",children:"Startseite"}),e.jsx("button",{type:"button",onClick:()=>o("services"),className:"suz-mobile-nav-link suz-focus-ring","aria-label":"Zu unseren Leistungen navigieren",children:"Leistungen"}),e.jsx("button",{type:"button",onClick:()=>o("team"),className:"suz-mobile-nav-link suz-focus-ring","aria-label":"Zu unserem Team navigieren",children:"Unser Team"}),e.jsx("button",{type:"button",onClick:()=>o("contact"),className:"suz-mobile-nav-link suz-focus-ring","aria-label":"Zum Kontakt navigieren",children:"Kontakt"})]})})]})]})},h=n.lazy(()=>c(()=>import("./Hero-C1Va492p.js"),__vite__mapDeps([0,1,2]))),x=n.lazy(()=>c(()=>import("./CompanyShowcase-zNc2jTdf.js"),__vite__mapDeps([3,1,2,4,5,6,7,8,9]))),v=n.lazy(()=>c(()=>import("./Services-DNGRjTaM.js"),__vite__mapDeps([10,1,2,4,5,6,7,8,9,11]))),f=n.lazy(()=>c(()=>import("./Team-AobfuSFs.js"),__vite__mapDeps([12,1,2]))),j=n.lazy(()=>c(()=>import("./Contact-C8oc20f7.js"),__vite__mapDeps([13,1,2,4,5,6,7,8,11]))),z=n.lazy(()=>c(()=>import("./Footer-CL2aL3cg.js"),__vite__mapDeps([14,1,2,4,5,6,7,8]))),r=({className:l=""})=>e.jsx("div",{className:`flex items-center justify-center py-16 ${l}`,children:e.jsx("div",{className:"w-8 h-8 border-2 border-blue-200 border-t-blue-600 rounded-full animate-spin"})}),S=()=>{const[l,s]=n.useState(0);n.useEffect(()=>{const a=()=>s(window.scrollY);return window.addEventListener("scroll",a),()=>window.removeEventListener("scroll",a)},[]),n.useEffect(()=>{console.log("🎨 SUZ Design System Debug:"),console.log("- Premium gradient class applied:",document.querySelector(".bg-premium-gradient")!==null),console.log("- Glass morphism class applied:",document.querySelector(".glass-morphism-premium")!==null),console.log("- Gradient text class applied:",document.querySelector(".gradient-text-animated")!==null),console.log("- Force Apple design class applied:",document.querySelector(".force-apple-design")!==null);const a=getComputedStyle(document.documentElement);console.log("- SUZ Blue Primary:",a.getPropertyValue("--suz-blue-primary")),console.log("- Current theme class:",document.documentElement.classList.contains("dark")?"dark":"light")},[]);const t=a=>{document.getElementById(a)?.scrollIntoView({behavior:"smooth"})};return e.jsxs("div",{className:"min-h-screen bg-premium-gradient overflow-x-hidden force-apple-design",children:[e.jsx(g,{scrollToSection:t}),e.jsx("div",{className:"fixed top-6 left-6 z-50 animate-fade-in suz-logo-container",children:e.jsx("div",{className:"suz-card-glass suz-logo-wrapper rounded-3xl border border-white/30 shadow-xl logo-glow group",children:e.jsx("img",{src:"/assets/logos/logo.png",alt:"SUZ Reinigung Logo",className:"suz-logo-enhanced object-contain transition-all duration-300 group-hover:scale-110 image-optimized"})})}),e.jsx(n.Suspense,{fallback:e.jsx(r,{className:"min-h-screen"}),children:e.jsx(h,{scrollY:l})}),e.jsx(n.Suspense,{fallback:e.jsx(r,{}),children:e.jsx(x,{})}),e.jsx(n.Suspense,{fallback:e.jsx(r,{}),children:e.jsx(v,{})}),e.jsx(n.Suspense,{fallback:e.jsx(r,{}),children:e.jsx(f,{})}),e.jsx(n.Suspense,{fallback:e.jsx(r,{}),children:e.jsx(j,{})}),e.jsx(n.Suspense,{fallback:e.jsx(r,{}),children:e.jsx(z,{})})]})};export{S as default};
