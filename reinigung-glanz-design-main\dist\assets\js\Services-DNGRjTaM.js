import{j as e}from"./ui-vendor-WY5xTeZk.js";import{c as n}from"./index-C_fHf9Ba.js";import{H as r}from"./house-CewbeNug.js";import{U as s}from"./users-CuNJVDcq.js";import"./react-vendor-o6ozJo2K.js";import"./utils-vendor--BulIq_u.js";import"./query-vendor--kaXPEoe.js";import"./router-vendor-CClBJTgV.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const l=n("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const o=n("Building2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const d=n("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const c=n("Sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]]),y=()=>{const a=[{title:"Hotelzimmerreinigung",description:"Tiefenreinigung und tägliche Pflege für höchste Hygienestandards in Hotelzimmern.",icon:o},{title:"Teppichreinigung",description:"Tiefenreinigung für Teppiche und Polster. Wir entfernen Flecken, Gerüche und Allergene für ein frisches und hygienisches Raumklima.",icon:r},{title:"Bodenreinigung",description:"Professionelle Pflege für Hartböden, Fliesen, Laminat und mehr. Wir sorgen für glänzende, hygienisch saubere Oberflächen.",icon:c},{title:"Gemeinschaftsräume",description:"Zuverlässige Reinigung von Treppenhäusern, Fluren und Gemeinschaftsbereichen für Mehrfamilienhäuser und Wohnanlagen.",icon:s},{title:"Büroreinigung",description:"Professionelle Reinigung von Büroflächen und Arbeitsplätzen für ein sauberes und produktives Arbeitsumfeld.",icon:l},{title:"Desinfektion",description:"Gründliche Desinfektion von Räumen und Oberflächen zur Bekämpfung von Keimen, Bakterien und Viren für maximale Hygiene und Sicherheit.",icon:d}];return e.jsx("section",{id:"services",className:"px-4",style:{padding:"var(--section-padding-xl) var(--space-4)",marginTop:"var(--space-16)"},children:e.jsxs("div",{className:"max-w-7xl mx-auto",children:[e.jsxs("div",{className:"text-center animate-fade-in",style:{marginBottom:"var(--space-20)"},children:[e.jsxs("h2",{className:"suz-section-title text-slate-100 mb-8",children:["Unsere ",e.jsx("span",{className:"gradient-text",children:"Leistungen"})]}),e.jsx("p",{className:"suz-text-heading-lg text-slate-300 max-w-3xl mx-auto",children:"Professionelle Reinigungslösungen für jeden Bedarf"})]}),e.jsx("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-10",children:a.map((i,t)=>e.jsxs("article",{className:"suz-card-glass service-card-premium card-hover-enhanced rounded-3xl border border-white/30 group shadow-2xl animate-fade-in relative overflow-hidden",style:{animationDelay:`${t*.15}s`,padding:"var(--component-padding-lg)"},children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 group-hover:animate-shimmer transition-opacity duration-500 pointer-events-none"}),e.jsx("div",{className:"icon-badge-enhanced mb-6 group-hover:scale-110 group-hover:rotate-3 relative z-10",role:"img","aria-label":`Icon für ${i.title}`,children:e.jsx(i.icon,{size:40,className:"text-blue-400 drop-shadow-lg transition-all duration-500 group-hover:text-blue-300 group-hover:scale-110",strokeWidth:2.5})}),e.jsx("h3",{className:"suz-text-heading-lg font-semibold text-slate-100 mb-6 text-reveal relative z-10",children:i.title}),e.jsx("p",{className:"suz-text-body-lg text-slate-300 text-reveal relative z-10",style:{lineHeight:"var(--line-height-relaxed)"},children:i.description})]},t))})]})})};export{y as default};
