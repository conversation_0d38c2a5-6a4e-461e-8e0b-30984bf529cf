const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/js/Index-BIrMGcU0.js","assets/js/ui-vendor-WY5xTeZk.js","assets/js/react-vendor-o6ozJo2K.js","assets/js/utils-vendor--BulIq_u.js","assets/js/query-vendor--kaXPEoe.js","assets/js/router-vendor-CClBJTgV.js","assets/js/NotFound-Cpc_r-E2.js"])))=>i.map(i=>d[i]);
import{j as m,V as Ue,R as qe,A as Je,C as Ge,T as Xe,D as Qe,P as Et,a as Ze,b as Tt}from"./ui-vendor-WY5xTeZk.js";import{a as St,r as x,o as n,v as Ct}from"./react-vendor-o6ozJo2K.js";import{t as Nt,c as Pt,a as kt}from"./utils-vendor--BulIq_u.js";import{Q as jt,a as It}from"./query-vendor--kaXPEoe.js";import{B as Rt,R as Mt,a as $e}from"./router-vendor-CClBJTgV.js";(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))r(s);new MutationObserver(s=>{for(const i of s)if(i.type==="childList")for(const d of i.addedNodes)d.tagName==="LINK"&&d.rel==="modulepreload"&&r(d)}).observe(document,{childList:!0,subtree:!0});function a(s){const i={};return s.integrity&&(i.integrity=s.integrity),s.referrerPolicy&&(i.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?i.credentials="include":s.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(s){if(s.ep)return;s.ep=!0;const i=a(s);fetch(s.href,i)}})();var et,_e=St;et=_e.createRoot,_e.hydrateRoot;const At="modulepreload",Lt=function(t){return"/"+t},Ye={},tt=function(e,a,r){let s=Promise.resolve();if(a&&a.length>0){document.getElementsByTagName("link");const d=document.querySelector("meta[property=csp-nonce]"),u=d?.nonce||d?.getAttribute("nonce");s=Promise.allSettled(a.map(c=>{if(c=Lt(c),c in Ye)return;Ye[c]=!0;const h=c.endsWith(".css"),y=h?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${c}"]${y}`))return;const N=document.createElement("link");if(N.rel=h?"stylesheet":At,h||(N.as="script"),N.crossOrigin="",N.href=c,u&&N.setAttribute("nonce",u),document.head.appendChild(N),h)return new Promise((X,o)=>{N.addEventListener("load",X),N.addEventListener("error",()=>o(new Error(`Unable to preload CSS for ${c}`)))})}))}function i(d){const u=new Event("vite:preloadError",{cancelable:!0});if(u.payload=d,window.dispatchEvent(u),!u.defaultPrevented)throw d}return s.then(d=>{for(const u of d||[])u.status==="rejected"&&i(u.reason);return e().catch(i)})},Ot=1,Bt=1e6;let Pe=0;function zt(){return Pe=(Pe+1)%Number.MAX_SAFE_INTEGER,Pe.toString()}const ke=new Map,Ve=t=>{if(ke.has(t))return;const e=setTimeout(()=>{ke.delete(t),oe({type:"REMOVE_TOAST",toastId:t})},Bt);ke.set(t,e)},Dt=(t,e)=>{switch(e.type){case"ADD_TOAST":return{...t,toasts:[e.toast,...t.toasts].slice(0,Ot)};case"UPDATE_TOAST":return{...t,toasts:t.toasts.map(a=>a.id===e.toast.id?{...a,...e.toast}:a)};case"DISMISS_TOAST":{const{toastId:a}=e;return a?Ve(a):t.toasts.forEach(r=>{Ve(r.id)}),{...t,toasts:t.toasts.map(r=>r.id===a||a===void 0?{...r,open:!1}:r)}}case"REMOVE_TOAST":return e.toastId===void 0?{...t,toasts:[]}:{...t,toasts:t.toasts.filter(a=>a.id!==e.toastId)}}},ge=[];let ve={toasts:[]};function oe(t){ve=Dt(ve,t),ge.forEach(e=>{e(ve)})}function Ft({...t}){const e=zt(),a=s=>oe({type:"UPDATE_TOAST",toast:{...s,id:e}}),r=()=>oe({type:"DISMISS_TOAST",toastId:e});return oe({type:"ADD_TOAST",toast:{...t,id:e,open:!0,onOpenChange:s=>{s||r()}}}),{id:e,dismiss:r,update:a}}function $t(){const[t,e]=x.useState(ve);return x.useEffect(()=>(ge.push(e),()=>{const a=ge.indexOf(e);a>-1&&ge.splice(a,1)}),[t]),{...t,toast:Ft,dismiss:a=>oe({type:"DISMISS_TOAST",toastId:a})}}/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _t=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),at=(...t)=>t.filter((e,a,r)=>!!e&&e.trim()!==""&&r.indexOf(e)===a).join(" ").trim();/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var Yt={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vt=x.forwardRef(({color:t="currentColor",size:e=24,strokeWidth:a=2,absoluteStrokeWidth:r,className:s="",children:i,iconNode:d,...u},c)=>x.createElement("svg",{ref:c,...Yt,width:e,height:e,stroke:t,strokeWidth:r?Number(a)*24/Number(e):a,className:at("lucide",s),...u},[...d.map(([h,y])=>x.createElement(h,y)),...Array.isArray(i)?i:[i]]));/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ht=(t,e)=>{const a=x.forwardRef(({className:r,...s},i)=>x.createElement(Vt,{ref:i,iconNode:e,className:at(`lucide-${_t(t)}`,r),...s}));return a.displayName=`${t}`,a};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wt=Ht("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);function H(...t){return Nt(Pt(t))}const Kt=Et,rt=x.forwardRef(({className:t,...e},a)=>m.jsx(Ue,{ref:a,className:H("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",t),...e}));rt.displayName=Ue.displayName;const Ut=kt("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),ot=x.forwardRef(({className:t,variant:e,...a},r)=>m.jsx(qe,{ref:r,className:H(Ut({variant:e}),t),...a}));ot.displayName=qe.displayName;const qt=x.forwardRef(({className:t,...e},a)=>m.jsx(Je,{ref:a,className:H("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",t),...e}));qt.displayName=Je.displayName;const st=x.forwardRef(({className:t,...e},a)=>m.jsx(Ge,{ref:a,className:H("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",t),"toast-close":"",...e,children:m.jsx(Wt,{className:"h-4 w-4"})}));st.displayName=Ge.displayName;const nt=x.forwardRef(({className:t,...e},a)=>m.jsx(Xe,{ref:a,className:H("text-sm font-semibold",t),...e}));nt.displayName=Xe.displayName;const it=x.forwardRef(({className:t,...e},a)=>m.jsx(Qe,{ref:a,className:H("text-sm opacity-90",t),...e}));it.displayName=Qe.displayName;function Jt(){const{toasts:t}=$t();return m.jsxs(Kt,{children:[t.map(function({id:e,title:a,description:r,action:s,...i}){return m.jsxs(ot,{...i,children:[m.jsxs("div",{className:"grid gap-1",children:[a&&m.jsx(nt,{children:a}),r&&m.jsx(it,{children:r})]}),s,m.jsx(st,{})]},e)}),m.jsx(rt,{})]})}var Gt=t=>{switch(t){case"success":return Zt;case"info":return ta;case"warning":return ea;case"error":return aa;default:return null}},Xt=Array(12).fill(0),Qt=({visible:t,className:e})=>n.createElement("div",{className:["sonner-loading-wrapper",e].filter(Boolean).join(" "),"data-visible":t},n.createElement("div",{className:"sonner-spinner"},Xt.map((a,r)=>n.createElement("div",{className:"sonner-loading-bar",key:`spinner-bar-${r}`})))),Zt=n.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},n.createElement("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z",clipRule:"evenodd"})),ea=n.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",height:"20",width:"20"},n.createElement("path",{fillRule:"evenodd",d:"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z",clipRule:"evenodd"})),ta=n.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},n.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z",clipRule:"evenodd"})),aa=n.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},n.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"})),ra=n.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"},n.createElement("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),n.createElement("line",{x1:"6",y1:"6",x2:"18",y2:"18"})),oa=()=>{let[t,e]=n.useState(document.hidden);return n.useEffect(()=>{let a=()=>{e(document.hidden)};return document.addEventListener("visibilitychange",a),()=>window.removeEventListener("visibilitychange",a)},[]),t},je=1,sa=class{constructor(){this.subscribe=t=>(this.subscribers.push(t),()=>{let e=this.subscribers.indexOf(t);this.subscribers.splice(e,1)}),this.publish=t=>{this.subscribers.forEach(e=>e(t))},this.addToast=t=>{this.publish(t),this.toasts=[...this.toasts,t]},this.create=t=>{var e;let{message:a,...r}=t,s=typeof t?.id=="number"||((e=t.id)==null?void 0:e.length)>0?t.id:je++,i=this.toasts.find(u=>u.id===s),d=t.dismissible===void 0?!0:t.dismissible;return this.dismissedToasts.has(s)&&this.dismissedToasts.delete(s),i?this.toasts=this.toasts.map(u=>u.id===s?(this.publish({...u,...t,id:s,title:a}),{...u,...t,id:s,dismissible:d,title:a}):u):this.addToast({title:a,...r,dismissible:d,id:s}),s},this.dismiss=t=>(this.dismissedToasts.add(t),t||this.toasts.forEach(e=>{this.subscribers.forEach(a=>a({id:e.id,dismiss:!0}))}),this.subscribers.forEach(e=>e({id:t,dismiss:!0})),t),this.message=(t,e)=>this.create({...e,message:t}),this.error=(t,e)=>this.create({...e,message:t,type:"error"}),this.success=(t,e)=>this.create({...e,type:"success",message:t}),this.info=(t,e)=>this.create({...e,type:"info",message:t}),this.warning=(t,e)=>this.create({...e,type:"warning",message:t}),this.loading=(t,e)=>this.create({...e,type:"loading",message:t}),this.promise=(t,e)=>{if(!e)return;let a;e.loading!==void 0&&(a=this.create({...e,promise:t,type:"loading",message:e.loading,description:typeof e.description!="function"?e.description:void 0}));let r=t instanceof Promise?t:t(),s=a!==void 0,i,d=r.then(async c=>{if(i=["resolve",c],n.isValidElement(c))s=!1,this.create({id:a,type:"default",message:c});else if(ia(c)&&!c.ok){s=!1;let h=typeof e.error=="function"?await e.error(`HTTP error! status: ${c.status}`):e.error,y=typeof e.description=="function"?await e.description(`HTTP error! status: ${c.status}`):e.description;this.create({id:a,type:"error",message:h,description:y})}else if(e.success!==void 0){s=!1;let h=typeof e.success=="function"?await e.success(c):e.success,y=typeof e.description=="function"?await e.description(c):e.description;this.create({id:a,type:"success",message:h,description:y})}}).catch(async c=>{if(i=["reject",c],e.error!==void 0){s=!1;let h=typeof e.error=="function"?await e.error(c):e.error,y=typeof e.description=="function"?await e.description(c):e.description;this.create({id:a,type:"error",message:h,description:y})}}).finally(()=>{var c;s&&(this.dismiss(a),a=void 0),(c=e.finally)==null||c.call(e)}),u=()=>new Promise((c,h)=>d.then(()=>i[0]==="reject"?h(i[1]):c(i[1])).catch(h));return typeof a!="string"&&typeof a!="number"?{unwrap:u}:Object.assign(a,{unwrap:u})},this.custom=(t,e)=>{let a=e?.id||je++;return this.create({jsx:t(a),id:a,...e}),a},this.getActiveToasts=()=>this.toasts.filter(t=>!this.dismissedToasts.has(t.id)),this.subscribers=[],this.toasts=[],this.dismissedToasts=new Set}},C=new sa,na=(t,e)=>{let a=e?.id||je++;return C.addToast({title:t,...e,id:a}),a},ia=t=>t&&typeof t=="object"&&"ok"in t&&typeof t.ok=="boolean"&&"status"in t&&typeof t.status=="number",la=na,ca=()=>C.toasts,da=()=>C.getActiveToasts();Object.assign(la,{success:C.success,info:C.info,warning:C.warning,error:C.error,custom:C.custom,message:C.message,promise:C.promise,dismiss:C.dismiss,loading:C.loading},{getHistory:ca,getToasts:da});function ua(t,{insertAt:e}={}){if(typeof document>"u")return;let a=document.head||document.getElementsByTagName("head")[0],r=document.createElement("style");r.type="text/css",e==="top"&&a.firstChild?a.insertBefore(r,a.firstChild):a.appendChild(r),r.styleSheet?r.styleSheet.cssText=t:r.appendChild(document.createTextNode(t))}ua(`:where(html[dir="ltr"]),:where([data-sonner-toaster][dir="ltr"]){--toast-icon-margin-start: -3px;--toast-icon-margin-end: 4px;--toast-svg-margin-start: -1px;--toast-svg-margin-end: 0px;--toast-button-margin-start: auto;--toast-button-margin-end: 0;--toast-close-button-start: 0;--toast-close-button-end: unset;--toast-close-button-transform: translate(-35%, -35%)}:where(html[dir="rtl"]),:where([data-sonner-toaster][dir="rtl"]){--toast-icon-margin-start: 4px;--toast-icon-margin-end: -3px;--toast-svg-margin-start: 0px;--toast-svg-margin-end: -1px;--toast-button-margin-start: 0;--toast-button-margin-end: auto;--toast-close-button-start: unset;--toast-close-button-end: 0;--toast-close-button-transform: translate(35%, -35%)}:where([data-sonner-toaster]){position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1: hsl(0, 0%, 99%);--gray2: hsl(0, 0%, 97.3%);--gray3: hsl(0, 0%, 95.1%);--gray4: hsl(0, 0%, 93%);--gray5: hsl(0, 0%, 90.9%);--gray6: hsl(0, 0%, 88.7%);--gray7: hsl(0, 0%, 85.8%);--gray8: hsl(0, 0%, 78%);--gray9: hsl(0, 0%, 56.1%);--gray10: hsl(0, 0%, 52.3%);--gray11: hsl(0, 0%, 43.5%);--gray12: hsl(0, 0%, 9%);--border-radius: 8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:none;z-index:999999999;transition:transform .4s ease}:where([data-sonner-toaster][data-lifted="true"]){transform:translateY(-10px)}@media (hover: none) and (pointer: coarse){:where([data-sonner-toaster][data-lifted="true"]){transform:none}}:where([data-sonner-toaster][data-x-position="right"]){right:var(--offset-right)}:where([data-sonner-toaster][data-x-position="left"]){left:var(--offset-left)}:where([data-sonner-toaster][data-x-position="center"]){left:50%;transform:translate(-50%)}:where([data-sonner-toaster][data-y-position="top"]){top:var(--offset-top)}:where([data-sonner-toaster][data-y-position="bottom"]){bottom:var(--offset-bottom)}:where([data-sonner-toast]){--y: translateY(100%);--lift-amount: calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);filter:blur(0);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:none;overflow-wrap:anywhere}:where([data-sonner-toast][data-styled="true"]){padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px #0000001a;width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}:where([data-sonner-toast]:focus-visible){box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast][data-y-position="top"]){top:0;--y: translateY(-100%);--lift: 1;--lift-amount: calc(1 * var(--gap))}:where([data-sonner-toast][data-y-position="bottom"]){bottom:0;--y: translateY(100%);--lift: -1;--lift-amount: calc(var(--lift) * var(--gap))}:where([data-sonner-toast]) :where([data-description]){font-weight:400;line-height:1.4;color:inherit}:where([data-sonner-toast]) :where([data-title]){font-weight:500;line-height:1.5;color:inherit}:where([data-sonner-toast]) :where([data-icon]){display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}:where([data-sonner-toast][data-promise="true"]) :where([data-icon])>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}:where([data-sonner-toast]) :where([data-icon])>*{flex-shrink:0}:where([data-sonner-toast]) :where([data-icon]) svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}:where([data-sonner-toast]) :where([data-content]){display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;cursor:pointer;outline:none;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}:where([data-sonner-toast]) :where([data-button]):focus-visible{box-shadow:0 0 0 2px #0006}:where([data-sonner-toast]) :where([data-button]):first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}:where([data-sonner-toast]) :where([data-cancel]){color:var(--normal-text);background:rgba(0,0,0,.08)}:where([data-sonner-toast][data-theme="dark"]) :where([data-cancel]){background:rgba(255,255,255,.3)}:where([data-sonner-toast]) :where([data-close-button]){position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast] [data-close-button]{background:var(--gray1)}:where([data-sonner-toast]) :where([data-close-button]):focus-visible{box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast]) :where([data-disabled="true"]){cursor:not-allowed}:where([data-sonner-toast]):hover :where([data-close-button]):hover{background:var(--gray2);border-color:var(--gray5)}:where([data-sonner-toast][data-swiping="true"]):before{content:"";position:absolute;left:-50%;right:-50%;height:100%;z-index:-1}:where([data-sonner-toast][data-y-position="top"][data-swiping="true"]):before{bottom:50%;transform:scaleY(3) translateY(50%)}:where([data-sonner-toast][data-y-position="bottom"][data-swiping="true"]):before{top:50%;transform:scaleY(3) translateY(-50%)}:where([data-sonner-toast][data-swiping="false"][data-removed="true"]):before{content:"";position:absolute;inset:0;transform:scaleY(2)}:where([data-sonner-toast]):after{content:"";position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}:where([data-sonner-toast][data-mounted="true"]){--y: translateY(0);opacity:1}:where([data-sonner-toast][data-expanded="false"][data-front="false"]){--scale: var(--toasts-before) * .05 + 1;--y: translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}:where([data-sonner-toast])>*{transition:opacity .4s}:where([data-sonner-toast][data-expanded="false"][data-front="false"][data-styled="true"])>*{opacity:0}:where([data-sonner-toast][data-visible="false"]){opacity:0;pointer-events:none}:where([data-sonner-toast][data-mounted="true"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}:where([data-sonner-toast][data-removed="true"][data-front="true"][data-swipe-out="false"]){--y: translateY(calc(var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="false"]){--y: translateY(40%);opacity:0;transition:transform .5s,opacity .2s}:where([data-sonner-toast][data-removed="true"][data-front="false"]):before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount-y, 0px)) translate(var(--swipe-amount-x, 0px));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation-duration:.2s;animation-timing-function:ease-out;animation-fill-mode:forwards}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=left]{animation-name:swipe-out-left}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=right]{animation-name:swipe-out-right}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=up]{animation-name:swipe-out-up}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=down]{animation-name:swipe-out-down}@keyframes swipe-out-left{0%{transform:var(--y) translate(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translate(calc(var(--swipe-amount-x) - 100%));opacity:0}}@keyframes swipe-out-right{0%{transform:var(--y) translate(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translate(calc(var(--swipe-amount-x) + 100%));opacity:0}}@keyframes swipe-out-up{0%{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) - 100%));opacity:0}}@keyframes swipe-out-down{0%{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) + 100%));opacity:0}}@media (max-width: 600px){[data-sonner-toaster]{position:fixed;right:var(--mobile-offset-right);left:var(--mobile-offset-left);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset-left) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset-left) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset-left)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--mobile-offset-bottom)}[data-sonner-toaster][data-y-position=top]{top:var(--mobile-offset-top)}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset-left);right:var(--mobile-offset-right);transform:none}}[data-sonner-toaster][data-theme=light]{--normal-bg: #fff;--normal-border: var(--gray4);--normal-text: var(--gray12);--success-bg: hsl(143, 85%, 96%);--success-border: hsl(145, 92%, 91%);--success-text: hsl(140, 100%, 27%);--info-bg: hsl(208, 100%, 97%);--info-border: hsl(221, 91%, 91%);--info-text: hsl(210, 92%, 45%);--warning-bg: hsl(49, 100%, 97%);--warning-border: hsl(49, 91%, 91%);--warning-text: hsl(31, 92%, 45%);--error-bg: hsl(359, 100%, 97%);--error-border: hsl(359, 100%, 94%);--error-text: hsl(360, 100%, 45%)}[data-sonner-toaster][data-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg: #fff;--normal-border: var(--gray3);--normal-text: var(--gray12)}[data-sonner-toaster][data-theme=dark]{--normal-bg: #000;--normal-bg-hover: hsl(0, 0%, 12%);--normal-border: hsl(0, 0%, 20%);--normal-border-hover: hsl(0, 0%, 25%);--normal-text: var(--gray1);--success-bg: hsl(150, 100%, 6%);--success-border: hsl(147, 100%, 12%);--success-text: hsl(150, 86%, 65%);--info-bg: hsl(215, 100%, 6%);--info-border: hsl(223, 100%, 12%);--info-text: hsl(216, 87%, 65%);--warning-bg: hsl(64, 100%, 6%);--warning-border: hsl(60, 100%, 12%);--warning-text: hsl(46, 87%, 65%);--error-bg: hsl(358, 76%, 10%);--error-border: hsl(357, 89%, 16%);--error-text: hsl(358, 100%, 81%)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast] [data-close-button]{background:var(--normal-bg);border-color:var(--normal-border);color:var(--normal-text)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast] [data-close-button]:hover{background:var(--normal-bg-hover);border-color:var(--normal-border-hover)}[data-rich-colors=true][data-sonner-toast][data-type=success],[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info],[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning],[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error],[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size: 16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:nth-child(1){animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}to{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}to{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}to{opacity:.15}}@media (prefers-reduced-motion){[data-sonner-toast],[data-sonner-toast]>*,.sonner-loading-bar{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}
`);function he(t){return t.label!==void 0}var ma=3,fa="32px",ha="16px",He=4e3,pa=356,ga=14,va=20,ya=200;function A(...t){return t.filter(Boolean).join(" ")}function ba(t){let[e,a]=t.split("-"),r=[];return e&&r.push(e),a&&r.push(a),r}var wa=t=>{var e,a,r,s,i,d,u,c,h,y,N;let{invert:X,toast:o,unstyled:ye,interacting:T,setHeights:L,visibleToasts:se,heights:W,index:z,toasts:be,expanded:Q,removeToast:j,defaultRichColors:Z,closeButton:ne,style:ie,cancelButtonStyle:we,actionButtonStyle:le,className:O="",descriptionClassName:ce="",duration:K,position:de,gap:_,loadingIcon:B,expandByDefault:ue,classNames:f,icons:P,closeButtonAriaLabel:xe="Close toast",pauseWhenPageIsHidden:g}=t,[v,w]=n.useState(null),[S,U]=n.useState(null),[E,Ee]=n.useState(!1),[ee,me]=n.useState(!1),[te,Te]=n.useState(!1),[Me,ct]=n.useState(!1),[dt,Ae]=n.useState(!1),[ut,Se]=n.useState(0),[mt,Le]=n.useState(0),ae=n.useRef(o.duration||K||He),Oe=n.useRef(null),Y=n.useRef(null),ft=z===0,ht=z+1<=se,k=o.type,q=o.dismissible!==!1,pt=o.className||"",gt=o.descriptionClassName||"",fe=n.useMemo(()=>W.findIndex(l=>l.toastId===o.id)||0,[W,o.id]),vt=n.useMemo(()=>{var l;return(l=o.closeButton)!=null?l:ne},[o.closeButton,ne]),Be=n.useMemo(()=>o.duration||K||He,[o.duration,K]),Ce=n.useRef(0),J=n.useRef(0),ze=n.useRef(0),G=n.useRef(null),[yt,bt]=de.split("-"),De=n.useMemo(()=>W.reduce((l,p,b)=>b>=fe?l:l+p.height,0),[W,fe]),Fe=oa(),wt=o.invert||X,Ne=k==="loading";J.current=n.useMemo(()=>fe*_+De,[fe,De]),n.useEffect(()=>{ae.current=Be},[Be]),n.useEffect(()=>{Ee(!0)},[]),n.useEffect(()=>{let l=Y.current;if(l){let p=l.getBoundingClientRect().height;return Le(p),L(b=>[{toastId:o.id,height:p,position:o.position},...b]),()=>L(b=>b.filter(I=>I.toastId!==o.id))}},[L,o.id]),n.useLayoutEffect(()=>{if(!E)return;let l=Y.current,p=l.style.height;l.style.height="auto";let b=l.getBoundingClientRect().height;l.style.height=p,Le(b),L(I=>I.find(R=>R.toastId===o.id)?I.map(R=>R.toastId===o.id?{...R,height:b}:R):[{toastId:o.id,height:b,position:o.position},...I])},[E,o.title,o.description,L,o.id]);let D=n.useCallback(()=>{me(!0),Se(J.current),L(l=>l.filter(p=>p.toastId!==o.id)),setTimeout(()=>{j(o)},ya)},[o,j,L,J]);n.useEffect(()=>{if(o.promise&&k==="loading"||o.duration===1/0||o.type==="loading")return;let l;return Q||T||g&&Fe?(()=>{if(ze.current<Ce.current){let p=new Date().getTime()-Ce.current;ae.current=ae.current-p}ze.current=new Date().getTime()})():ae.current!==1/0&&(Ce.current=new Date().getTime(),l=setTimeout(()=>{var p;(p=o.onAutoClose)==null||p.call(o,o),D()},ae.current)),()=>clearTimeout(l)},[Q,T,o,k,g,Fe,D]),n.useEffect(()=>{o.delete&&D()},[D,o.delete]);function xt(){var l,p,b;return P!=null&&P.loading?n.createElement("div",{className:A(f?.loader,(l=o?.classNames)==null?void 0:l.loader,"sonner-loader"),"data-visible":k==="loading"},P.loading):B?n.createElement("div",{className:A(f?.loader,(p=o?.classNames)==null?void 0:p.loader,"sonner-loader"),"data-visible":k==="loading"},B):n.createElement(Qt,{className:A(f?.loader,(b=o?.classNames)==null?void 0:b.loader),visible:k==="loading"})}return n.createElement("li",{tabIndex:0,ref:Y,className:A(O,pt,f?.toast,(e=o?.classNames)==null?void 0:e.toast,f?.default,f?.[k],(a=o?.classNames)==null?void 0:a[k]),"data-sonner-toast":"","data-rich-colors":(r=o.richColors)!=null?r:Z,"data-styled":!(o.jsx||o.unstyled||ye),"data-mounted":E,"data-promise":!!o.promise,"data-swiped":dt,"data-removed":ee,"data-visible":ht,"data-y-position":yt,"data-x-position":bt,"data-index":z,"data-front":ft,"data-swiping":te,"data-dismissible":q,"data-type":k,"data-invert":wt,"data-swipe-out":Me,"data-swipe-direction":S,"data-expanded":!!(Q||ue&&E),style:{"--index":z,"--toasts-before":z,"--z-index":be.length-z,"--offset":`${ee?ut:J.current}px`,"--initial-height":ue?"auto":`${mt}px`,...ie,...o.style},onDragEnd:()=>{Te(!1),w(null),G.current=null},onPointerDown:l=>{Ne||!q||(Oe.current=new Date,Se(J.current),l.target.setPointerCapture(l.pointerId),l.target.tagName!=="BUTTON"&&(Te(!0),G.current={x:l.clientX,y:l.clientY}))},onPointerUp:()=>{var l,p,b,I;if(Me||!q)return;G.current=null;let R=Number(((l=Y.current)==null?void 0:l.style.getPropertyValue("--swipe-amount-x").replace("px",""))||0),F=Number(((p=Y.current)==null?void 0:p.style.getPropertyValue("--swipe-amount-y").replace("px",""))||0),V=new Date().getTime()-((b=Oe.current)==null?void 0:b.getTime()),M=v==="x"?R:F,$=Math.abs(M)/V;if(Math.abs(M)>=va||$>.11){Se(J.current),(I=o.onDismiss)==null||I.call(o,o),U(v==="x"?R>0?"right":"left":F>0?"down":"up"),D(),ct(!0),Ae(!1);return}Te(!1),w(null)},onPointerMove:l=>{var p,b,I,R;if(!G.current||!q||((p=window.getSelection())==null?void 0:p.toString().length)>0)return;let F=l.clientY-G.current.y,V=l.clientX-G.current.x,M=(b=t.swipeDirections)!=null?b:ba(de);!v&&(Math.abs(V)>1||Math.abs(F)>1)&&w(Math.abs(V)>Math.abs(F)?"x":"y");let $={x:0,y:0};v==="y"?(M.includes("top")||M.includes("bottom"))&&(M.includes("top")&&F<0||M.includes("bottom")&&F>0)&&($.y=F):v==="x"&&(M.includes("left")||M.includes("right"))&&(M.includes("left")&&V<0||M.includes("right")&&V>0)&&($.x=V),(Math.abs($.x)>0||Math.abs($.y)>0)&&Ae(!0),(I=Y.current)==null||I.style.setProperty("--swipe-amount-x",`${$.x}px`),(R=Y.current)==null||R.style.setProperty("--swipe-amount-y",`${$.y}px`)}},vt&&!o.jsx?n.createElement("button",{"aria-label":xe,"data-disabled":Ne,"data-close-button":!0,onClick:Ne||!q?()=>{}:()=>{var l;D(),(l=o.onDismiss)==null||l.call(o,o)},className:A(f?.closeButton,(s=o?.classNames)==null?void 0:s.closeButton)},(i=P?.close)!=null?i:ra):null,o.jsx||x.isValidElement(o.title)?o.jsx?o.jsx:typeof o.title=="function"?o.title():o.title:n.createElement(n.Fragment,null,k||o.icon||o.promise?n.createElement("div",{"data-icon":"",className:A(f?.icon,(d=o?.classNames)==null?void 0:d.icon)},o.promise||o.type==="loading"&&!o.icon?o.icon||xt():null,o.type!=="loading"?o.icon||P?.[k]||Gt(k):null):null,n.createElement("div",{"data-content":"",className:A(f?.content,(u=o?.classNames)==null?void 0:u.content)},n.createElement("div",{"data-title":"",className:A(f?.title,(c=o?.classNames)==null?void 0:c.title)},typeof o.title=="function"?o.title():o.title),o.description?n.createElement("div",{"data-description":"",className:A(ce,gt,f?.description,(h=o?.classNames)==null?void 0:h.description)},typeof o.description=="function"?o.description():o.description):null),x.isValidElement(o.cancel)?o.cancel:o.cancel&&he(o.cancel)?n.createElement("button",{"data-button":!0,"data-cancel":!0,style:o.cancelButtonStyle||we,onClick:l=>{var p,b;he(o.cancel)&&q&&((b=(p=o.cancel).onClick)==null||b.call(p,l),D())},className:A(f?.cancelButton,(y=o?.classNames)==null?void 0:y.cancelButton)},o.cancel.label):null,x.isValidElement(o.action)?o.action:o.action&&he(o.action)?n.createElement("button",{"data-button":!0,"data-action":!0,style:o.actionButtonStyle||le,onClick:l=>{var p,b;he(o.action)&&((b=(p=o.action).onClick)==null||b.call(p,l),!l.defaultPrevented&&D())},className:A(f?.actionButton,(N=o?.classNames)==null?void 0:N.actionButton)},o.action.label):null))};function We(){if(typeof window>"u"||typeof document>"u")return"ltr";let t=document.documentElement.getAttribute("dir");return t==="auto"||!t?window.getComputedStyle(document.documentElement).direction:t}function xa(t,e){let a={};return[t,e].forEach((r,s)=>{let i=s===1,d=i?"--mobile-offset":"--offset",u=i?ha:fa;function c(h){["top","right","bottom","left"].forEach(y=>{a[`${d}-${y}`]=typeof h=="number"?`${h}px`:h})}typeof r=="number"||typeof r=="string"?c(r):typeof r=="object"?["top","right","bottom","left"].forEach(h=>{r[h]===void 0?a[`${d}-${h}`]=u:a[`${d}-${h}`]=typeof r[h]=="number"?`${r[h]}px`:r[h]}):c(u)}),a}var Ea=x.forwardRef(function(t,e){let{invert:a,position:r="bottom-right",hotkey:s=["altKey","KeyT"],expand:i,closeButton:d,className:u,offset:c,mobileOffset:h,theme:y="light",richColors:N,duration:X,style:o,visibleToasts:ye=ma,toastOptions:T,dir:L=We(),gap:se=ga,loadingIcon:W,icons:z,containerAriaLabel:be="Notifications",pauseWhenPageIsHidden:Q}=t,[j,Z]=n.useState([]),ne=n.useMemo(()=>Array.from(new Set([r].concat(j.filter(g=>g.position).map(g=>g.position)))),[j,r]),[ie,we]=n.useState([]),[le,O]=n.useState(!1),[ce,K]=n.useState(!1),[de,_]=n.useState(y!=="system"?y:typeof window<"u"&&window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),B=n.useRef(null),ue=s.join("+").replace(/Key/g,"").replace(/Digit/g,""),f=n.useRef(null),P=n.useRef(!1),xe=n.useCallback(g=>{Z(v=>{var w;return(w=v.find(S=>S.id===g.id))!=null&&w.delete||C.dismiss(g.id),v.filter(({id:S})=>S!==g.id)})},[]);return n.useEffect(()=>C.subscribe(g=>{if(g.dismiss){Z(v=>v.map(w=>w.id===g.id?{...w,delete:!0}:w));return}setTimeout(()=>{Ct.flushSync(()=>{Z(v=>{let w=v.findIndex(S=>S.id===g.id);return w!==-1?[...v.slice(0,w),{...v[w],...g},...v.slice(w+1)]:[g,...v]})})})}),[]),n.useEffect(()=>{if(y!=="system"){_(y);return}if(y==="system"&&(window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?_("dark"):_("light")),typeof window>"u")return;let g=window.matchMedia("(prefers-color-scheme: dark)");try{g.addEventListener("change",({matches:v})=>{_(v?"dark":"light")})}catch{g.addListener(({matches:w})=>{try{_(w?"dark":"light")}catch(S){console.error(S)}})}},[y]),n.useEffect(()=>{j.length<=1&&O(!1)},[j]),n.useEffect(()=>{let g=v=>{var w,S;s.every(U=>v[U]||v.code===U)&&(O(!0),(w=B.current)==null||w.focus()),v.code==="Escape"&&(document.activeElement===B.current||(S=B.current)!=null&&S.contains(document.activeElement))&&O(!1)};return document.addEventListener("keydown",g),()=>document.removeEventListener("keydown",g)},[s]),n.useEffect(()=>{if(B.current)return()=>{f.current&&(f.current.focus({preventScroll:!0}),f.current=null,P.current=!1)}},[B.current]),n.createElement("section",{ref:e,"aria-label":`${be} ${ue}`,tabIndex:-1,"aria-live":"polite","aria-relevant":"additions text","aria-atomic":"false",suppressHydrationWarning:!0},ne.map((g,v)=>{var w;let[S,U]=g.split("-");return j.length?n.createElement("ol",{key:g,dir:L==="auto"?We():L,tabIndex:-1,ref:B,className:u,"data-sonner-toaster":!0,"data-theme":de,"data-y-position":S,"data-lifted":le&&j.length>1&&!i,"data-x-position":U,style:{"--front-toast-height":`${((w=ie[0])==null?void 0:w.height)||0}px`,"--width":`${pa}px`,"--gap":`${se}px`,...o,...xa(c,h)},onBlur:E=>{P.current&&!E.currentTarget.contains(E.relatedTarget)&&(P.current=!1,f.current&&(f.current.focus({preventScroll:!0}),f.current=null))},onFocus:E=>{E.target instanceof HTMLElement&&E.target.dataset.dismissible==="false"||P.current||(P.current=!0,f.current=E.relatedTarget)},onMouseEnter:()=>O(!0),onMouseMove:()=>O(!0),onMouseLeave:()=>{ce||O(!1)},onDragEnd:()=>O(!1),onPointerDown:E=>{E.target instanceof HTMLElement&&E.target.dataset.dismissible==="false"||K(!0)},onPointerUp:()=>K(!1)},j.filter(E=>!E.position&&v===0||E.position===g).map((E,Ee)=>{var ee,me;return n.createElement(wa,{key:E.id,icons:z,index:Ee,toast:E,defaultRichColors:N,duration:(ee=T?.duration)!=null?ee:X,className:T?.className,descriptionClassName:T?.descriptionClassName,invert:a,visibleToasts:ye,closeButton:(me=T?.closeButton)!=null?me:d,interacting:ce,position:g,style:T?.style,unstyled:T?.unstyled,classNames:T?.classNames,cancelButtonStyle:T?.cancelButtonStyle,actionButtonStyle:T?.actionButtonStyle,removeToast:xe,toasts:j.filter(te=>te.position==E.position),heights:ie.filter(te=>te.position==E.position),setHeights:we,expandByDefault:i,gap:se,loadingIcon:W,expanded:le,pauseWhenPageIsHidden:Q,swipeDirections:t.swipeDirections})})):null}))});const Ta=({...t})=>m.jsx(Ea,{theme:"dark",className:"toaster group",toastOptions:{classNames:{toast:"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",description:"group-[.toast]:text-muted-foreground",actionButton:"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",cancelButton:"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground"}},...t}),Sa=Tt,Ca=x.forwardRef(({className:t,sideOffset:e=4,...a},r)=>m.jsx(Ze,{ref:r,sideOffset:e,className:H("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),...a}));Ca.displayName=Ze.displayName;const Na=x.createContext(void 0);function Pa({children:t}){const e="dark";x.useEffect(()=>{const s=document.documentElement;s.classList.remove("light"),s.classList.add("dark"),localStorage.removeItem("suz-theme");const i=document.querySelector('meta[name="theme-color"]');i&&i.setAttribute("content","#111827")},[]);const a=()=>{},r=()=>{};return m.jsx(Na.Provider,{value:{theme:e,toggleTheme:a,setTheme:r},children:t})}const ka=x.lazy(()=>tt(()=>import("./Index-BIrMGcU0.js"),__vite__mapDeps([0,1,2,3,4,5]))),ja=x.lazy(()=>tt(()=>import("./NotFound-Cpc_r-E2.js"),__vite__mapDeps([6,1,2,5]))),Ia=()=>m.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-blue-50",children:m.jsxs("div",{className:"text-center space-y-4",children:[m.jsx("div",{className:"w-12 h-12 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto"}),m.jsx("p",{className:"text-slate-600 font-medium",children:"Lädt..."})]})}),Ra=new jt,Ma=()=>m.jsx(It,{client:Ra,children:m.jsx(Pa,{children:m.jsxs(Sa,{children:[m.jsx(Jt,{}),m.jsx(Ta,{}),m.jsx(Rt,{children:m.jsx(x.Suspense,{fallback:m.jsx(Ia,{}),children:m.jsxs(Mt,{children:[m.jsx($e,{path:"/",element:m.jsx(ka,{})}),m.jsx($e,{path:"*",element:m.jsx(ja,{})})]})})})]})})}),Ie={IMAGES:{name:"suz-images",version:"1.0.0",maxAge:24*60*60*1e3,maxEntries:100},API:{name:"suz-api",version:"1.0.0",maxAge:5*60*1e3,maxEntries:50},STATIC:{name:"suz-static",version:"1.0.0",maxAge:7*24*60*60*1e3,maxEntries:200}};class Re{config;storageKey;constructor(e){this.config=e,this.storageKey=`${e.name}-${e.version}`}async set(e,a){try{const r=Date.now(),s={data:a,timestamp:r,expires:r+this.config.maxAge},i=await this.getCache();i[e]=s,await this.enforceMaxEntries(i),localStorage.setItem(this.storageKey,JSON.stringify(i))}catch(r){console.warn("[Cache] Failed to set cache entry:",r)}}async get(e){try{const a=await this.getCache(),r=a[e];return r?Date.now()>r.expires?(delete a[e],localStorage.setItem(this.storageKey,JSON.stringify(a)),null):r.data:null}catch(a){return console.warn("[Cache] Failed to get cache entry:",a),null}}async has(e){return await this.get(e)!==null}async delete(e){try{const a=await this.getCache();delete a[e],localStorage.setItem(this.storageKey,JSON.stringify(a))}catch(a){console.warn("[Cache] Failed to delete cache entry:",a)}}async clear(){try{localStorage.removeItem(this.storageKey)}catch(e){console.warn("[Cache] Failed to clear cache:",e)}}async getStats(){try{const e=await this.getCache(),a=Object.values(e),r=a.map(s=>s.timestamp);return{size:JSON.stringify(e).length,entries:a.length,oldestEntry:r.length>0?Math.min(...r):null,newestEntry:r.length>0?Math.max(...r):null}}catch(e){return console.warn("[Cache] Failed to get cache stats:",e),{size:0,entries:0,oldestEntry:null,newestEntry:null}}}async cleanup(){try{const e=await this.getCache(),a=Date.now();let r=0;for(const[s,i]of Object.entries(e))a>i.expires&&(delete e[s],r++);return r>0&&localStorage.setItem(this.storageKey,JSON.stringify(e)),r}catch(e){return console.warn("[Cache] Failed to cleanup cache:",e),0}}async getCache(){try{const e=localStorage.getItem(this.storageKey);return e?JSON.parse(e):{}}catch(e){return console.warn("[Cache] Failed to parse cache, resetting:",e),{}}}async enforceMaxEntries(e){const a=Object.entries(e);if(a.length<=this.config.maxEntries)return;a.sort(([,s],[,i])=>s.timestamp-i.timestamp);const r=a.length-this.config.maxEntries;for(let s=0;s<r;s++)delete e[a[s][0]]}}const lt=new Re(Ie.IMAGES),Aa=new Re(Ie.API),La=new Re(Ie.STATIC);async function Oa(){const e=["/assets/logos/logo.png","/assets/images/hero-bg.jpg"].map(async a=>{try{const r=await fetch(a);if(r.ok){const s=await r.blob();await lt.set(a,URL.createObjectURL(s))}}catch(r){console.warn(`[Cache] Failed to preload ${a}:`,r)}});await Promise.allSettled(e)}async function Ba(){await Promise.all([lt.cleanup(),Aa.cleanup(),La.cleanup()]),await Oa(),console.log("[Cache] Cache system initialized")}const za={LCP:{good:2500,poor:4e3},FID:{good:100,poor:300},CLS:{good:.1,poor:.25},FCP:{good:1800,poor:3e3},TTFB:{good:800,poor:1800}};function re(t,e){const a=za[t];return e<=a.good?"good":e<=a.poor?"needs-improvement":"poor"}class Da{constructor(e){this.onMetric=e,this.initializeObservers()}metrics={};observers=[];initializeObservers(){this.observeLCP(),this.observeFID(),this.observeCLS(),this.observeFCP(),this.observeTTFB()}observeLCP(){if("PerformanceObserver"in window)try{const e=new PerformanceObserver(a=>{const r=a.getEntries(),s=r[r.length-1];if(s){const i=s.renderTime||s.loadTime||s.startTime,d={name:"LCP",value:i,rating:re("LCP",i),delta:i-(this.metrics.lcp?.value||0),id:`lcp-${Date.now()}`};this.metrics.lcp=d,this.onMetric?.(d)}});e.observe({type:"largest-contentful-paint",buffered:!0}),this.observers.push(e)}catch(e){console.warn("[Performance] LCP observer failed:",e)}}observeFID(){if("PerformanceObserver"in window)try{const e=new PerformanceObserver(a=>{a.getEntries().forEach(s=>{const i=s.processingStart-s.startTime,d={name:"FID",value:i,rating:re("FID",i),delta:i-(this.metrics.fid?.value||0),id:`fid-${Date.now()}`};this.metrics.fid=d,this.onMetric?.(d)})});e.observe({type:"first-input",buffered:!0}),this.observers.push(e)}catch(e){console.warn("[Performance] FID observer failed:",e)}}observeCLS(){if("PerformanceObserver"in window)try{let e=0,a=0,r=[];const s=new PerformanceObserver(i=>{i.getEntries().forEach(u=>{if(!u.hadRecentInput){const c=r[0],h=r[r.length-1];if(a&&u.startTime-h.startTime<1e3&&u.startTime-c.startTime<5e3?(a+=u.value,r.push(u)):(a=u.value,r=[u]),a>e){e=a;const y={name:"CLS",value:e,rating:re("CLS",e),delta:e-(this.metrics.cls?.value||0),id:`cls-${Date.now()}`};this.metrics.cls=y,this.onMetric?.(y)}}})});s.observe({type:"layout-shift",buffered:!0}),this.observers.push(s)}catch(e){console.warn("[Performance] CLS observer failed:",e)}}observeFCP(){if("PerformanceObserver"in window)try{const e=new PerformanceObserver(a=>{a.getEntries().forEach(s=>{if(s.name==="first-contentful-paint"){const i=s.startTime,d={name:"FCP",value:i,rating:re("FCP",i),delta:i-(this.metrics.fcp?.value||0),id:`fcp-${Date.now()}`};this.metrics.fcp=d,this.onMetric?.(d)}})});e.observe({type:"paint",buffered:!0}),this.observers.push(e)}catch(e){console.warn("[Performance] FCP observer failed:",e)}}observeTTFB(){try{const e=performance.getEntriesByType("navigation")[0];if(e){const a=e.responseStart-e.requestStart,r={name:"TTFB",value:a,rating:re("TTFB",a),delta:a-(this.metrics.ttfb?.value||0),id:`ttfb-${Date.now()}`};this.metrics.ttfb=r,this.onMetric?.(r)}}catch(e){console.warn("[Performance] TTFB measurement failed:",e)}}getMetrics(){return{...this.metrics}}getPerformanceScore(){const e=Object.values(this.metrics);if(e.length===0)return 0;const a=e.map(r=>{switch(r.rating){case"good":return 100;case"needs-improvement":return 75;case"poor":return 50;default:return 0}});return Math.round(a.reduce((r,s)=>r+s,0)/a.length)}disconnect(){this.observers.forEach(e=>e.disconnect()),this.observers=[]}}class pe{static preloadCriticalResources(){[{href:"/assets/logos/logo.png",as:"image"},{href:"/assets/images/hero-bg.jpg",as:"image"}].forEach(({href:a,as:r})=>{const s=document.createElement("link");s.rel="preload",s.href=a,s.as=r,document.head.appendChild(s)})}static optimizeImages(){document.querySelectorAll("img[data-optimize]").forEach(a=>{const r=a;r.loading||(r.loading="lazy"),r.decoding="async",(!r.width||!r.height)&&(r.style.aspectRatio="16/9")})}static reduceLayoutShifts(){document.querySelectorAll("img:not([width]):not([height])").forEach(r=>{const s=r;s.style.aspectRatio="16/9",s.style.width="100%",s.style.height="auto"}),document.querySelectorAll("[data-dynamic]").forEach(r=>{const s=r;s.style.minHeight||(s.style.minHeight="200px")})}static optimizeFonts(){const e=document.createElement("style");e.textContent=`
      @font-face {
        font-family: -apple-system;
        font-display: swap;
      }
    `,document.head.appendChild(e)}}let Ke=null;function Fa(){Ke||(Ke=new Da(t=>{console.log(`[Performance] ${t.name}:`,{value:Math.round(t.value),rating:t.rating,delta:Math.round(t.delta)})}),pe.preloadCriticalResources(),pe.optimizeImages(),pe.reduceLayoutShifts(),pe.optimizeFonts(),console.log("[Performance] Performance monitoring initialized"))}"serviceWorker"in navigator&&window.addEventListener("load",()=>{navigator.serviceWorker.register("/sw.js").then(t=>{console.log("SW registered: ",t),t.addEventListener("updatefound",()=>{const e=t.installing;e&&e.addEventListener("statechange",()=>{e.state==="installed"&&navigator.serviceWorker.controller&&confirm("Neue Version verfügbar. Seite neu laden?")&&(e.postMessage({type:"SKIP_WAITING"}),window.location.reload())})})}).catch(t=>{console.log("SW registration failed: ",t)})});Ba().catch(console.error);Fa();document.body.classList.add("force-apple-design");et(document.getElementById("root")).render(m.jsx(Ma,{}));export{tt as _,Ht as c};
