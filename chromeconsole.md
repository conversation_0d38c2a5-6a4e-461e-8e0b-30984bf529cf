react-router-dom.js?v=530f7785:4393 ⚠️ React Router Future Flag Warning: React Router will begin wrapping state updates in `React.startTransition` in v7. You can use the `v7_startTransition` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_starttransition.
warnOnce @ react-router-dom.js?v=530f7785:4393
logDeprecation @ react-router-dom.js?v=530f7785:4396
logV6DeprecationWarnings @ react-router-dom.js?v=530f7785:4399
(anonymous) @ react-router-dom.js?v=530f7785:5271
commitHookEffectListMount @ chunk-WERSD76P.js?v=530f7785:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=530f7785:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=530f7785:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=530f7785:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=530f7785:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=530f7785:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=530f7785:19447
performSyncWorkOnRoot @ chunk-WERSD76P.js?v=530f7785:18868
flushSyncCallbacks @ chunk-WERSD76P.js?v=530f7785:9119
commitRootImpl @ chunk-WERSD76P.js?v=530f7785:19432
commitRoot @ chunk-WERSD76P.js?v=530f7785:19277
finishConcurrentRender @ chunk-WERSD76P.js?v=530f7785:18783
performConcurrentWorkOnRoot @ chunk-WERSD76P.js?v=530f7785:18718
workLoop @ chunk-WERSD76P.js?v=530f7785:197
flushWork @ chunk-WERSD76P.js?v=530f7785:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=530f7785:384
react-router-dom.js?v=530f7785:4393 ⚠️ React Router Future Flag Warning: Relative route resolution within Splat routes is changing in v7. You can use the `v7_relativeSplatPath` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_relativesplatpath.
warnOnce @ react-router-dom.js?v=530f7785:4393
logDeprecation @ react-router-dom.js?v=530f7785:4396
logV6DeprecationWarnings @ react-router-dom.js?v=530f7785:4402
(anonymous) @ react-router-dom.js?v=530f7785:5271
commitHookEffectListMount @ chunk-WERSD76P.js?v=530f7785:16915
commitPassiveMountOnFiber @ chunk-WERSD76P.js?v=530f7785:18156
commitPassiveMountEffects_complete @ chunk-WERSD76P.js?v=530f7785:18129
commitPassiveMountEffects_begin @ chunk-WERSD76P.js?v=530f7785:18119
commitPassiveMountEffects @ chunk-WERSD76P.js?v=530f7785:18109
flushPassiveEffectsImpl @ chunk-WERSD76P.js?v=530f7785:19490
flushPassiveEffects @ chunk-WERSD76P.js?v=530f7785:19447
performSyncWorkOnRoot @ chunk-WERSD76P.js?v=530f7785:18868
flushSyncCallbacks @ chunk-WERSD76P.js?v=530f7785:9119
commitRootImpl @ chunk-WERSD76P.js?v=530f7785:19432
commitRoot @ chunk-WERSD76P.js?v=530f7785:19277
finishConcurrentRender @ chunk-WERSD76P.js?v=530f7785:18783
performConcurrentWorkOnRoot @ chunk-WERSD76P.js?v=530f7785:18718
workLoop @ chunk-WERSD76P.js?v=530f7785:197
flushWork @ chunk-WERSD76P.js?v=530f7785:176
performWorkUntilDeadline @ chunk-WERSD76P.js?v=530f7785:384
localhost/:1 The resource http://localhost:8080/assets/images/hero-bg.jpg was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.
localhost/:1 The resource http://localhost:8080/assets/images/hero-bg.jpg was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.
