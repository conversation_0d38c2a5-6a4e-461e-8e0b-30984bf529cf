<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile Navigation Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            color: white;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }
        .viewport-info {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 1000;
        }
        .test-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #2563eb;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: 1px solid #333;
            border-radius: 10px;
        }
    </style>
</head>
<body>
    <div class="viewport-info" id="viewportInfo">
        Viewport: <span id="dimensions"></span><br>
        Mobile: <span id="isMobile"></span>
    </div>

    <div class="test-container">
        <h1>Mobile Navigation Test</h1>
        
        <div class="test-section">
            <h2>Viewport Information</h2>
            <p>Current viewport size: <span id="currentSize"></span></p>
            <p>Mobile breakpoint: 768px</p>
            <p>Status: <span id="mobileStatus"></span></p>
        </div>

        <div class="test-section">
            <h2>Test Different Viewport Sizes</h2>
            <button class="test-button" onclick="testViewport(320, 568)">Mobile (320x568)</button>
            <button class="test-button" onclick="testViewport(768, 1024)">Tablet (768x1024)</button>
            <button class="test-button" onclick="testViewport(1024, 768)">Desktop (1024x768)</button>
            <button class="test-button" onclick="testViewport(1920, 1080)">Large Desktop (1920x1080)</button>
        </div>

        <div class="test-section">
            <h2>Live Website Preview</h2>
            <iframe id="websiteFrame" src="http://localhost:8081"></iframe>
        </div>

        <div class="test-section">
            <h2>Test Instructions</h2>
            <ol>
                <li><strong>Desktop Test (>768px):</strong> Should show centered navigation bar with all menu items visible</li>
                <li><strong>Mobile Test (≤768px):</strong> Should show hamburger menu in top-right corner only</li>
                <li><strong>Hamburger Menu:</strong> Click to open/close mobile menu with smooth animation</li>
                <li><strong>Menu Items:</strong> Should navigate correctly and close menu after selection</li>
                <li><strong>Responsive Transition:</strong> Resize browser to test smooth transition at 768px breakpoint</li>
            </ol>
        </div>
    </div>

    <script>
        function updateViewportInfo() {
            const width = window.innerWidth;
            const height = window.innerHeight;
            const isMobile = width < 768;
            
            document.getElementById('dimensions').textContent = `${width}x${height}`;
            document.getElementById('isMobile').textContent = isMobile ? 'Yes' : 'No';
            document.getElementById('currentSize').textContent = `${width}x${height}`;
            document.getElementById('mobileStatus').textContent = isMobile ? 'Mobile View' : 'Desktop View';
            document.getElementById('mobileStatus').style.color = isMobile ? '#10b981' : '#3b82f6';
        }

        function testViewport(width, height) {
            // This would require browser dev tools to actually resize
            alert(`To test ${width}x${height}:\n1. Open browser dev tools (F12)\n2. Toggle device toolbar\n3. Set custom dimensions to ${width}x${height}\n4. Observe navigation behavior`);
        }

        // Update viewport info on load and resize
        updateViewportInfo();
        window.addEventListener('resize', updateViewportInfo);

        // Auto-refresh iframe every 5 seconds to catch changes
        setInterval(() => {
            const iframe = document.getElementById('websiteFrame');
            iframe.src = iframe.src;
        }, 5000);
    </script>
</body>
</html>
